package com.wosai.upay.job.service;

import com.googlecode.jsonrpc4j.JsonRpcService;
import com.wosai.upay.job.model.ContractSubTask;
import org.springframework.validation.annotation.Validated;

/**
 * @Author: 郑冬
 * @date: 2025/07/25 14:55
 * @Description: 中投科信payway=0进件成功之后由contract进行回调
 */
@JsonRpcService("/rpc/umb/callback")
@Validated
public interface UMBCallBackService {

    /**
     * 将payway=0的参数写入
     * @param subTask 进件子任务,当前分账子任务是最后一个子任务,所以这里是分账子任务
     * @param providerMerchantId 中投科信子商户号
     */
    void umbCallBackFromContract(ContractSubTask subTask,String providerMerchantId);

}
