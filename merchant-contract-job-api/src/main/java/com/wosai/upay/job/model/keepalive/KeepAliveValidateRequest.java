package com.wosai.upay.job.model.keepalive;

import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import javax.validation.constraints.AssertTrue;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;

/**
 * <AUTHOR>
 * @date 2025/8/29
 */
@Data
public class KeepAliveValidateRequest {

    @NotBlank(message = "商户号不能为空")
    private String merchantSn;

    @NotBlank(message = "场景信息不能为空")
    @Pattern(regexp = "OPEN_CIRCLE|PRE_EXECUTE_CIRCLE|EXECUTE_STRATEGY", message = "场景信息格式错误")
    private String scenario;

    private String taskId;

    @AssertTrue(message = "参数校验失败")
    public boolean isValid() {
        if (StringUtils.equals("PRE_EXECUTE_CIRCLE", scenario) || StringUtils.equals("EXECUTE_STRATEGY", scenario)) {
            return !StringUtils.isBlank(taskId);
        }
        return true;
    }
}
