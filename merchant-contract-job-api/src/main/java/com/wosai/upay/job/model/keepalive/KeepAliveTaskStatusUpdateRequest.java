package com.wosai.upay.job.model.keepalive;

import com.wosai.common.utils.WosaiStringUtils;
import com.wosai.upay.job.validation.In;
import lombok.Data;

import javax.validation.constraints.AssertTrue;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Objects;

/**
 * 保活任务状态更新请求参数
 *
 * <AUTHOR>
@Data
public class KeepAliveTaskStatusUpdateRequest {

    /**
     * 任务ID
     */
    @NotBlank(message = "任务ID不能为空")
    private String taskId;

    /**
     * 状态 1:成功 2:失败
     */
    @In(values = {1, 2}, message = "状态枚举错误")
    @NotNull(message = "状态不能为空")
    private Integer status;

    /**
     * 来源
     */
    @NotBlank(message = "来源不能为空")
    private String source;

    /**
     * 失败原因
     */
    private String reason;

    /**
     * 订单号
     */
    private String orderNo;

    /**
     * 订单金额
     */
    private Long amount;

    /**
     * 支付时间
     */
    private Long payTime;

    @AssertTrue(message = "状态为成功时，订单号、订单金额和支付时间不能为空；状态为失败时，失败原因不能为空")
    public boolean isValid() {
        if (status == 1) {
            return WosaiStringUtils.isNotEmpty(orderNo) && !Objects.isNull(amount) && !Objects.isNull(payTime);
        } else {
            return WosaiStringUtils.isNotEmpty(reason);
        }
    }
}