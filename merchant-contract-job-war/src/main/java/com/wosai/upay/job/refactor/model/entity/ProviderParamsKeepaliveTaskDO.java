package com.wosai.upay.job.refactor.model.entity;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.Version;
import com.wosai.common.utils.WosaiStringUtils;
import com.wosai.upay.job.refactor.model.bo.KeepAliveTaskResultBO;
import com.wosai.upay.job.refactor.model.enums.ProviderParamsKeepaliveTaskStatusEnum;
import com.wosai.upay.merchant.contract.exception.ContractBizException;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 参数保活任务表实体对象
 *
 * <AUTHOR>
@TableName("provider_params_keepalive_task")
@Data
@Slf4j
public class ProviderParamsKeepaliveTaskDO {

    private static final String PROCESS_KEY = "process";
    private static final DateTimeFormatter DATETIME_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 任务id
     */
    @TableField(value = "task_id")
    private Long taskId;

    /**
     * 保活类型，1：系统自动保活；2：人工保活
     */
    @TableField(value = "type")
    private Integer type;

    /**
     * 商户号
     */
    @TableField(value = "merchant_sn")
    private String merchantSn;

    /**
     * 保活通道
     */
    @TableField(value = "provider")
    private Integer provider;

    /**
     * 支付方式
     */
    @TableField(value = "payway")
    private Integer payway;

    /**
     * 银联商户号
     */
    @TableField(value = "provider_mch_id")
    private String providerMchId;

    /**
     * 微信支付宝子商户号
     */
    @TableField(value = "sub_mch_id")
    private String subMchId;

    /**
     * 保活开始日期
     */
    @TableField(value = "start_date")
    private LocalDate startDate;

    /**
     * 保活结束日期
     */
    @TableField(value = "end_date")
    private LocalDate endDate;

    /**
     * 状态；1:待保活；2:保活中；3:成功；4：失败；5：取消
     */
    @TableField(value = "status")
    private Integer status;

    /**
     * 描述，失败原因或跳过原因
     */
    @TableField(value = "result")
    private String result;

    /**
     * 保活成功的交易时间
     */
    @TableField(value = "pay_time")
    private Long payTime;

    /**
     * 最后一笔订单支付金额
     */
    @TableField(value = "pay_amount")
    private Long payAmount;

    /**
     * 保活单号
     */
    @TableField(value = "pay_order")
    private String payOrder;

    /**
     * 本次保活使用的参数信息
     */
    @TableField(value = "params_snapshot")
    private String paramsSnapshot;

    /**
     * 扩展信息
     */
    @TableField(value = "extra")
    private String extra;

    /**
     * 创建时间
     */
    @TableField(value = "ctime")
    private LocalDateTime ctime;

    /**
     * 更新时间
     */
    @TableField(value = "mtime")
    private LocalDateTime mtime;

    /**
     * 版本号
     */
    @Version
    @TableField(value = "version")
    private Integer version;

    /**
     * 删除标识 0未删除 1已删除
     */
    @TableField(value = "deleted")
    private Integer deleted;

    /**
     * 更新 result 字段并在 extra 中记录操作流水
     *
     * @param newResult 新的 result 值
     */
    public void updateResultWithProcess(KeepAliveTaskResultBO newResult) {
        // 更新 result 字段
        this.result = JSON.toJSONString(newResult);

        // 在 extra 中记录操作流水
        try {
            // 解析现有的 extra 字段
            Map<String, Object> extraMap = parseExtraMap(this.extra);

            // 获取或创建 process 列表
            List<Map<String, Object>> processList = getOrCreateProcessList(extraMap);

            // 创建新的流水记录
            Map<String, Object> processRecord = createProcessRecord(this.result);
            processList.add(processRecord);

            // 更新 extra 字段
            extraMap.put(PROCESS_KEY, processList);
            this.extra = JSON.toJSONString(extraMap);
        } catch (Exception e) {
            log.error("更新 result 并添加操作记录失败，任务ID: {}", this.id, e);
        }
    }

    /**
     * 获取任务的所有 result 操作记录
     *
     * @return result 操作记录列表
     */
    @SuppressWarnings("unchecked")
    public List<Map<String, Object>> getProcessRecords() {
        if (WosaiStringUtils.isEmpty(this.extra)) {
            return new ArrayList<>();
        }

        try {
            Map<String, Object> extraMap = parseExtraMap(this.extra);
            Object processObj = extraMap.get(PROCESS_KEY);

            if (processObj instanceof List) {
                return (List<Map<String, Object>>) processObj;
            }
        } catch (Exception e) {
            log.error("获取 process 记录失败，任务ID: {}", this.id, e);
        }

        return new ArrayList<>();
    }

    /**
     * 解析 extra 字段为 Map
     *
     * @param extra extra 字段值
     * @return 解析后的 Map
     */
    private Map<String, Object> parseExtraMap(String extra) {
        if (WosaiStringUtils.isEmpty(extra)) {
            return new HashMap<>();
        }

        try {
            JSONObject jsonObject = JSON.parseObject(extra);
            return jsonObject != null ? jsonObject : new HashMap<>();
        } catch (Exception e) {
            log.warn("解析 extra 字段失败，将使用空 Map: {}", extra, e);
            return new HashMap<>();
        }
    }

    /**
     * 获取或创建 process 列表
     *
     * @param extraMap extra Map
     * @return process 列表
     */
    @SuppressWarnings("unchecked")
    private List<Map<String, Object>> getOrCreateProcessList(Map<String, Object> extraMap) {
        Object processObj = extraMap.get(PROCESS_KEY);

        if (processObj instanceof List) {
            try {
                return (List<Map<String, Object>>) processObj;
            } catch (ClassCastException e) {
                log.warn("process 字段类型不匹配，将创建新的列表", e);
            }
        }

        List<Map<String, Object>> newProcessList = new ArrayList<>();
        extraMap.put(PROCESS_KEY, newProcessList);
        return newProcessList;
    }

    /**
     * 创建流水记录
     *
     * @param result result 值
     * @return 流水记录 Map
     */
    private Map<String, Object> createProcessRecord(String result) {
        Map<String, Object> record = new HashMap<>();
        record.put("result", result);
        record.put("timestamp", LocalDateTime.now().format(DATETIME_FORMATTER));
        return record;
    }

    // ==================== 充血模型：业务状态管理方法 ====================

    /**
     * 启动保活任务
     *
     * @param paramsSnapshot 参数快照
     */
    public void startKeepAlive(String paramsSnapshot, KeepAliveTaskResultBO resultBO) {
        if (!canStart()) {
            throw new ContractBizException("任务状态不允许启动，当前状态: " + this.status);
        }

        this.status = ProviderParamsKeepaliveTaskStatusEnum.ACTIVE.getValue();
        this.paramsSnapshot = paramsSnapshot;
        updateResultWithProcess(resultBO);
        touchMtime();
    }

    /**
     * 完成保活任务（成功）
     *
     * @param payOrder  支付订单号
     * @param payAmount 支付金额
     * @param payTime   支付时间
     * @param resultBO  结果
     */
    public void completeSuccessfully(String payOrder, Long payAmount, Long payTime, KeepAliveTaskResultBO resultBO) {
        if (!canComplete()) {
            throw new ContractBizException("任务状态不允许完成，当前状态: " + this.status);
        }

        this.status = ProviderParamsKeepaliveTaskStatusEnum.SUCCESS.getValue();
        this.payOrder = payOrder;
        this.payAmount = payAmount;
        this.payTime = payTime;
        updateResultWithProcess(resultBO);
        touchMtime();
    }

    /**
     * 失败保活任务
     *
     * @param resultBO 失败原因
     */
    public void fail(KeepAliveTaskResultBO resultBO) {
        if (!canFail()) {
            throw new ContractBizException("任务状态不允许失败，当前状态: " + this.status);
        }

        this.status = ProviderParamsKeepaliveTaskStatusEnum.FAILED.getValue();
        updateResultWithProcess(resultBO);
        touchMtime();
    }

    /**
     * 取消保活任务
     *
     * @param resultBO 取消原因
     */
    public void cancel(KeepAliveTaskResultBO resultBO) {
        if (!canCancel()) {
            throw new ContractBizException("任务状态不允许取消，当前状态: " + this.status);
        }

        this.status = ProviderParamsKeepaliveTaskStatusEnum.CANCELLED.getValue();
        updateResultWithProcess(resultBO);
        touchMtime();
    }

    private void touchMtime() {
        this.mtime = LocalDateTime.now();
    }

    /**
     * 检查是否可以启动
     */
    public boolean canStart() {
        return ProviderParamsKeepaliveTaskStatusEnum.PENDING.getValue().equals(this.status);
    }

    /**
     * 检查是否可以完成
     */
    public boolean canComplete() {
        return ProviderParamsKeepaliveTaskStatusEnum.ACTIVE.getValue().equals(this.status);
    }

    /**
     * 检查是否可以失败
     */
    public boolean canFail() {
        return ProviderParamsKeepaliveTaskStatusEnum.ACTIVE.getValue().equals(this.status) ||
                ProviderParamsKeepaliveTaskStatusEnum.PENDING.getValue().equals(this.status);
    }

    /**
     * 检查是否可以取消
     */
    public boolean canCancel() {
        return ProviderParamsKeepaliveTaskStatusEnum.PENDING.getValue().equals(this.status) ||
                ProviderParamsKeepaliveTaskStatusEnum.ACTIVE.getValue().equals(this.status);
    }

    /**
     * 检查任务是否已完成（成功、失败或取消）
     */
    public boolean isCompleted() {
        return ProviderParamsKeepaliveTaskStatusEnum.SUCCESS.getValue().equals(this.status) ||
                ProviderParamsKeepaliveTaskStatusEnum.FAILED.getValue().equals(this.status) ||
                ProviderParamsKeepaliveTaskStatusEnum.CANCELLED.getValue().equals(this.status);
    }

    /**
     * 检查任务是否正在进行中
     */
    public boolean isActive() {
        return ProviderParamsKeepaliveTaskStatusEnum.ACTIVE.getValue().equals(this.status);
    }

    /**
     * 检查任务是否等待中
     */
    public boolean isPending() {
        return ProviderParamsKeepaliveTaskStatusEnum.PENDING.getValue().equals(this.status);
    }
}