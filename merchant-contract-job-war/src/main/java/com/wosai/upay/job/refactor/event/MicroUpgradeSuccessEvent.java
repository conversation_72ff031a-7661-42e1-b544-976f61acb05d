package com.wosai.upay.job.refactor.event;

import lombok.Getter;
import org.springframework.context.ApplicationEvent;

/**
 * <AUTHOR>
 * @date 2025/8/27
 */
@Getter
public class MicroUpgradeSuccessEvent extends ApplicationEvent {

    /**
     * 商户号
     */
    private String merchantSn;

    public MicroUpgradeSuccessEvent(Object source, String merchantSn) {
        super(source);
        this.merchantSn = merchantSn;
    }
}
