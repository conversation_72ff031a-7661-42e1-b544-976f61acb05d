package com.wosai.upay.job.refactor.model.entity;

import java.sql.Timestamp;
import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

/**
 * 子商户号最后一次交易时间表实体对象
 *
 * <AUTHOR>
@TableName("sub_mch_id_last_trade_time")
@Data
public class SubMchIdLastTradeTimeDO {

    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 商户号
     */
    @TableField(value = "merchant_sn")
    private String merchantSn;

    /**
     * 保活通道
     */
    @TableField(value = "provider")
    private Integer provider;

    /**
     * 支付方式
     */
    @TableField(value = "payway")
    private Integer payway;

    /**
     * 微信支付宝子商户号
     */
    @TableField(value = "sub_mch_id")
    private String subMchId;

    /**
     * 交易时间
     */
    @TableField(value = "trade_time")
    private Long tradeTime;

    /**
     * 创建时间
     */
    @TableField(value = "ctime")
    private LocalDateTime ctime;

    /**
     * 更新时间
     */
    @TableField(value = "mtime")
    private LocalDateTime mtime;
}