package com.wosai.upay.job.refactor.event;

import com.wosai.upay.job.biz.keepalive.KeepAliveConfigBiz;
import com.wosai.upay.job.biz.keepalive.KeepAliveTaskBiz;
import com.wosai.upay.job.model.keepalive.KeepAliveConfigResult;
import com.wosai.upay.job.refactor.model.bo.KeepAliveTaskResultBO;
import com.wosai.upay.job.refactor.model.enums.MerchantKeepAliveConfigStatusEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationListener;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2025/8/27
 */
@Slf4j
@Component
public class MicroUpgradeSuccessEventListener implements ApplicationListener<MicroUpgradeSuccessEvent> {

    @Autowired
    private KeepAliveConfigBiz keepAliveConfigBiz;
    @Autowired
    private KeepAliveTaskBiz keepAliveTaskBiz;

    @Override
    public void onApplicationEvent(MicroUpgradeSuccessEvent event) {
        String merchantSn = event.getMerchantSn();
        try {
            KeepAliveConfigResult keepAliveConfigResult = keepAliveConfigBiz.queryKeepAliveConfig(merchantSn);
            if (Objects.nonNull(keepAliveConfigResult) &&
                    MerchantKeepAliveConfigStatusEnum.OPEN.getValue().equals(keepAliveConfigResult.getStatus())) {
                keepAliveTaskBiz.createKeepAliveTasks(merchantSn,
                        KeepAliveTaskResultBO.createResultFromEvent("MicroUpgradeSuccessEventListener", "小微升级"));
            }
        } catch (Exception e) {
            log.error("小微升级保活任务创建异常，商户: {}", merchantSn, e);
        }
    }
}
