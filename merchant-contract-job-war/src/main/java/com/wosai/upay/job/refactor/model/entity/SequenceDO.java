package com.wosai.upay.job.refactor.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2025/9/2
 */
@TableName("sequence")
@Data
public class SequenceDO {

    @TableId(type = IdType.AUTO)
    private Long id;
    @TableField(value = "namespace")
    private String namespace;
    @TableField(value = "serial_no_base")
    private Long serialNoBase;
    @TableField(value = "batch_size")
    private Integer batchSize;
    @TableField("ctime")
    private LocalDateTime ctime;
    @TableField("mtime")
    private LocalDateTime mtime;
    @TableField("version")
    private Long version;
}
