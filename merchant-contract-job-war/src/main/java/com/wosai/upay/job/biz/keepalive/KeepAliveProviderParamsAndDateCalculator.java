package com.wosai.upay.job.biz.keepalive;

import com.wosai.common.utils.WosaiCollectionUtils;
import com.wosai.common.utils.WosaiMapUtils;
import com.wosai.upay.job.adapter.apollo.ApplicationApolloConfig;
import com.wosai.upay.job.adapter.apollo.KeepAliveApolloConfig;
import com.wosai.upay.job.adapter.apollo.model.KeepAliveCheckRuleConfig;
import com.wosai.upay.job.adapter.apollo.model.KeepAliveConfigModel;
import com.wosai.upay.job.refactor.dao.ContractStatusDAO;
import com.wosai.upay.job.refactor.dao.McProviderDAO;
import com.wosai.upay.job.refactor.dao.MerchantProviderParamsDAO;
import com.wosai.upay.job.refactor.dao.SubMchIdLastTradeTimeDAO;
import com.wosai.upay.job.refactor.model.entity.ContractStatusDO;
import com.wosai.upay.job.refactor.model.entity.McProviderDO;
import com.wosai.upay.job.refactor.model.entity.MerchantProviderParamsDO;
import com.wosai.upay.job.refactor.model.entity.SubMchIdLastTradeTimeDO;
import com.wosai.upay.merchant.contract.exception.ContractBizException;
import com.wosai.upay.merchant.contract.model.Tuple2;
import lombok.Getter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/8/14
 */
@Component
public class KeepAliveProviderParamsAndDateCalculator {

    @Autowired
    private ContractStatusDAO contractStatusDAO;
    @Autowired
    private MerchantProviderParamsDAO merchantProviderParamsDAO;
    @Autowired
    private SubMchIdLastTradeTimeDAO subMchIdLastTradeTimeDAO;
    @Autowired
    private McProviderDAO mcProviderDAO;
    @Autowired
    private KeepAliveApolloConfig keepAliveApolloConfig;

    /**
     * 计算商户需要保活的参数和对应的保活日期（包含最大保活日期）
     * 保活日期基于最后一次交易时间或参数创建时间计算，这里不做日期的去重处理
     *
     * @param merchantSn 商户号
     * @return 包含保活日期和最大保活日期的结果
     */
    public KeepAliveParamsAndDateResult calculateKeepAliveParamsAndDateWithMaxLimit(String merchantSn) {
        KeepAliveConfigModel keepAliveConfigModel = keepAliveApolloConfig.getKeepAliveConfigModel();
        // 获取所有可能需要保活的参数
        List<MerchantProviderParamsDO> params = calculateKeepAliveParams(merchantSn);

        // 计算每个参数的保活日期和最大保活日期
        Map<String, Integer> periodDelayDay = keepAliveConfigModel.getPeriodDelayDay();
        Map<String, Integer> maxDelayDay = keepAliveConfigModel.getMaxDelayDay();
        Map<MerchantProviderParamsDO, LocalDate> paramsAndDates = new LinkedHashMap<>();
        Map<MerchantProviderParamsDO, LocalDate> paramsAndMaxDates = new LinkedHashMap<>();

        params.forEach(param -> {
            String configKey = String.format("%s_%s", param.getProvider(), param.getPayway());

            // 获取延迟天数配置
            Integer periodDay = WosaiMapUtils.getInteger(periodDelayDay, configKey,
                    WosaiMapUtils.getInteger(periodDelayDay, "default"));

            // 获取最大延迟天数配置
            Integer maxPeriodDay = WosaiMapUtils.getInteger(maxDelayDay, configKey,
                    WosaiMapUtils.getInteger(maxDelayDay, "default"));

            // 计算保活日期和最大保活日期
            Tuple2<LocalDate, LocalDate> dates = calculateKeepAliveDate(param, periodDay, maxPeriodDay);
            paramsAndDates.put(param, dates.get_1());
            // 计算最大保活日期
            paramsAndMaxDates.put(param, dates.get_2());
        });

        return new KeepAliveParamsAndDateResult(paramsAndDates, paramsAndMaxDates);
    }

    /**
     * 计算商户需要保活的参数列表
     *
     * @param merchantSn 商户序列号
     * @return 需要保活的商户通道参数列表
     * @throws ContractBizException 当商户进件状态不存在时抛出异常
     */
    public List<MerchantProviderParamsDO> calculateKeepAliveParams(String merchantSn) {
        // 获取商户当前生效的收单机构
        Optional<ContractStatusDO> contractStatus = contractStatusDAO.getByMerchantSn(merchantSn);
        if (!contractStatus.isPresent()) {
            throw new ContractBizException("商户进件状态不存在");
        }
        KeepAliveConfigModel keepAliveConfigModel = keepAliveApolloConfig.getKeepAliveConfigModel();
        // 获取所有可能需要保活的参数
        return getAllKeepAliveParams(merchantSn, contractStatus.get().getAcquirer(), keepAliveConfigModel);
    }

    /**
     * 获取所有需要保活的参数
     * 去除当前在用的 provider，根据 provider进行顺序获取，保证得到的结果最终是按照指定 provider排序的
     *
     * @param merchantSn           商户号
     * @param currentAcquirer      当前生效的收单机构
     * @param keepAliveConfigModel apollo相关配置
     * @return 需要保活的参数
     */
    private List<MerchantProviderParamsDO> getAllKeepAliveParams(String merchantSn, String currentAcquirer, KeepAliveConfigModel keepAliveConfigModel) {
        // 1. 获取需要保活的 Provider 列表（去除当前在用的 provider）
        List<Integer> keepAliveProviders = getKeepAliveProviders(currentAcquirer, keepAliveConfigModel);
        if (WosaiCollectionUtils.isEmpty(keepAliveProviders)) {
            return new ArrayList<>();
        }

        // 2. 根据商户号、支付方式列表和 Provider 列表获取交易参数（已按创建时间倒序排序）
        List<MerchantProviderParamsDO> allParams = merchantProviderParamsDAO
                .getKeepAliveParamsSorted(merchantSn, keepAliveConfigModel.getKeepAlivePayways(), keepAliveProviders);
        if (WosaiCollectionUtils.isEmpty(allParams)) {
            return new ArrayList<>();
        }

        // 3. 使用工具类选择最优的保活参数
        return KeepAliveParamsSelector.selectKeepAliveParams(allParams, keepAliveProviders, keepAliveConfigModel.getKeepAlivePayways(), keepAliveConfigModel.getMaxKeepAliveProvidersSize());
    }

    /**
     * 获取需要保活的 Provider 列表
     * 从预定义的 PROVIDER_LIST 中去除当前在用的 provider
     *
     * @param currentAcquirer 当前生效的收单机构
     * @return 需要保活的 Provider 列表
     */
    private List<Integer> getKeepAliveProviders(String currentAcquirer, KeepAliveConfigModel keepAliveConfigModel) {
        List<Integer> keepAliveProviders = new ArrayList<>(keepAliveConfigModel.getKeepAliveProviders());

        // 获取当前收单机构使用的 provider 列表
        List<McProviderDO> currentProviderList = mcProviderDAO.getByAcquirer(currentAcquirer);
        if (WosaiCollectionUtils.isNotEmpty(currentProviderList)) {
            List<Integer> currentUseProviders = currentProviderList.stream()
                    .map(provider -> Integer.valueOf(provider.getProvider()))
                    .collect(Collectors.toList());
            keepAliveProviders.removeAll(currentUseProviders);
        }

        return keepAliveProviders;
    }


    /**
     * 计算单个参数的保活日期
     *
     * 业务逻辑：
     * 1. 计算基础保活日期（起始时间 + 延迟天数）
     * 2. 计算最大保活日期（起始时间 + 最大延迟天数）
     * 3. 根据业务规则调整最终保活日期
     *
     * @param param       参数
     * @param delayDay    延迟天数
     * @param maxDelayDay 最大延迟天数
     * @return 保活日期
     */
    private Tuple2<LocalDate, LocalDate> calculateKeepAliveDate(MerchantProviderParamsDO param, int delayDay, int maxDelayDay) {
        // 1. 确定起始时间（优先使用最后一次交易时间，如果没有则使用参数创建时间）
        long startTime = subMchIdLastTradeTimeDAO.selectByMerchantSnAndSubMchId(param.getMerchantSn(), param.getPayMerchantId())
                .map(SubMchIdLastTradeTimeDO::getTradeTime)
                .orElse(param.getCtime());

        // 2. 计算关键日期
        LocalDate baseDate = Instant.ofEpochMilli(startTime)
                .atZone(ZoneId.systemDefault())
                .toLocalDate();

        LocalDate normalKeepAliveDate = baseDate.plusDays(delayDay);      // 基础保活日期
        LocalDate maxKeepAliveDate = baseDate.plusDays(maxDelayDay);      // 最大保活日期
        LocalDate today = LocalDateTime.now().toLocalDate();  // 今天
        LocalDate nextDelayDate = today.plusDays(delayDay);  // 从今天起的延迟日期

        // 3. 应用业务规则决定最终保活日期
        LocalDate keepAliveDate = applyKeepAliveDateRules(normalKeepAliveDate, maxKeepAliveDate, today, nextDelayDate);
        return new Tuple2<>(keepAliveDate, maxKeepAliveDate);
    }

    /**
     * 应用保活日期业务规则
     *
     * 规则：
     * 1. 如果最大保活日期已过期或为今天，直接使用最大保活日期
     * 2. 如果正常保活日期在未来，使用正常保活日期
     * 3. 如果正常保活日期已过期或为今天，使用从今天起的延迟日期与最大保活日期之间的较早者
     *
     * @param normalKeepAliveDate 正常保活日期（起始时间 + 延迟天数）
     * @param maxKeepAliveDate    最大保活日期（起始时间 + 最大延迟天数）
     * @param today               今天
     * @param nextDelayDate       从今天起的延迟日期
     * @return 最终保活日期
     */
    private LocalDate applyKeepAliveDateRules(LocalDate normalKeepAliveDate,
                                              LocalDate maxKeepAliveDate,
                                              LocalDate today,
                                              LocalDate nextDelayDate) {

        // 规则1：如果最大保活日期已过期或为今天，直接使用最大保活日期
        if (!maxKeepAliveDate.isAfter(today)) {
            return maxKeepAliveDate;
        }

        // 规则2：如果正常保活日期在未来，使用正常保活日期
        if (normalKeepAliveDate.isAfter(today)) {
            return normalKeepAliveDate;
        }

        // 规则3：如果正常保活日期已过期或为今天，使用从今天起的延迟日期与最大保活日期之间的较早者
        return nextDelayDate.isBefore(maxKeepAliveDate) ? nextDelayDate : maxKeepAliveDate;
    }


    /**
     * 保活参数和日期结果类
     * 包含保活日期和最大保活日期的映射
     */
    @Getter
    public static class KeepAliveParamsAndDateResult {
        private final Map<MerchantProviderParamsDO, LocalDate> paramsAndDates;
        private final Map<MerchantProviderParamsDO, LocalDate> paramsAndMaxDates;

        public KeepAliveParamsAndDateResult(Map<MerchantProviderParamsDO, LocalDate> paramsAndDates,
                                          Map<MerchantProviderParamsDO, LocalDate> paramsAndMaxDates) {
            this.paramsAndDates = paramsAndDates;
            this.paramsAndMaxDates = paramsAndMaxDates;
        }

        public LocalDate getMaxKeepAliveDate(MerchantProviderParamsDO param) {
            return paramsAndMaxDates.get(param);
        }
    }

}
