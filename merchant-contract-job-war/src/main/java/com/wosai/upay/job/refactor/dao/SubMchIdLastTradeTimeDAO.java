package com.wosai.upay.job.refactor.dao;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.wosai.upay.job.refactor.mapper.SubMchIdLastTradeTimeDynamicMapper;
import com.wosai.upay.job.refactor.model.entity.SubMchIdLastTradeTimeDO;
import org.apache.ibatis.session.SqlSessionFactory;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * 子商户号最后一次交易时间表数据库访问层 {@link SubMchIdLastTradeTimeDO}
 * 对SubMchIdLastTradeTimeDynamicMapper层做出简单封装 {@link SubMchIdLastTradeTimeDynamicMapper}
 *
 * <AUTHOR>
@Repository
public class SubMchIdLastTradeTimeDAO extends AbstractBaseDAO<SubMchIdLastTradeTimeDO, SubMchIdLastTradeTimeDynamicMapper> {

    public SubMchIdLastTradeTimeDAO(SqlSessionFactory sqlSessionFactory, SubMchIdLastTradeTimeDynamicMapper entityMapper) {
        super(sqlSessionFactory, entityMapper);
    }

    /**
     * 根据子商户号查询记录
     *
     * @param subMchId 子商户号
     * @return 子商户号最后交易时间记录
     */
    public Optional<SubMchIdLastTradeTimeDO> selectByMerchantSnAndSubMchId(String merchantSn, String subMchId) {
        LambdaQueryWrapper<SubMchIdLastTradeTimeDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SubMchIdLastTradeTimeDO::getMerchantSn, merchantSn);
        queryWrapper.eq(SubMchIdLastTradeTimeDO::getSubMchId, subMchId);
        return selectOne(queryWrapper);
    }

    public List<SubMchIdLastTradeTimeDO> selectByMerchantSn(String merchantSn) {
        LambdaQueryWrapper<SubMchIdLastTradeTimeDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SubMchIdLastTradeTimeDO::getMerchantSn, merchantSn);
        return entityMapper.selectList(queryWrapper);
    }

    public Integer update(SubMchIdLastTradeTimeDO lastTradeTimeDO) {
        lastTradeTimeDO.setMtime(LocalDateTime.now());
        int affectedRows = entityMapper.updateById(lastTradeTimeDO);
        if (affectedRows < 1) {
            throw new RuntimeException("更新子商户号最后交易时间失败，请重试");
        }
        return affectedRows;
    }
}