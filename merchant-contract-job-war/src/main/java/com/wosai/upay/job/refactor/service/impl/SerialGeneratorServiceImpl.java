package com.wosai.upay.job.refactor.service.impl;

import com.wosai.upay.job.config.SequenceConfig;
import com.wosai.upay.job.enume.SequenceNamespaceEnum;
import com.wosai.upay.job.refactor.service.SerialGeneratorService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.ZoneId;

/**
 * <AUTHOR>
 * @date 2025/9/2
 */
@Service
public class SerialGeneratorServiceImpl implements SerialGeneratorService {

    private static final long ID_SUFFIX_MOD_BASE = 10000L;

    // 基准时间: 2022-01-01 00:00:00 UTC
    // 基准时间的毫秒时间戳
    private static final long EPOCH_MILLIS = LocalDateTime.of(2022, 1, 1, 0, 0, 0)
            .atZone(ZoneId.systemDefault()).toInstant().toEpochMilli();


    // 时间戳位数(9位) + 序列号位数(4位) = 13位
    private static final String ID_FORMAT = "%09d%04d";

    @Resource
    private SequenceConfig sequenceConfig;

    @Override
    public long genKeepAliveTaskId() {
        long relativeTimestamp = getRelativeTimestamp();
        long sequence = sequenceConfig.genSequence(SequenceNamespaceEnum.KEEP_ALIVE.getNamespace());
        return generateId(relativeTimestamp, sequence);
    }

    /**
     * 获取相对时间戳（从基准时间开始的秒数）
     */
    private long getRelativeTimestamp() {
        return (System.currentTimeMillis() - EPOCH_MILLIS) / 1000;
    }

    /**
     * 生成13位固定长度ID
     * 格式: 9位相对时间戳 + 4位序列号
     */
    private long generateId(long relativeTimestamp, long sequence) {
        long sequenceNumber = sequence % ID_SUFFIX_MOD_BASE;
        String idStr = String.format(ID_FORMAT, relativeTimestamp, sequenceNumber);
        return Long.parseLong(idStr);
    }
}
