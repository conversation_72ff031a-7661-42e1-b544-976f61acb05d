package com.wosai.upay.job.biz.keepalive;

import com.alibaba.fastjson.JSON;
import com.wosai.common.utils.WosaiCollectionUtils;
import com.wosai.cua.kafka.avro.MerchantKeepAliveParams;
import com.wosai.upay.job.refactor.model.entity.ProviderParamsKeepaliveTaskDO;
import com.wosai.upay.job.refactor.model.enums.ProviderParamsKeepaliveTaskStatusEnum;
import com.wosai.upay.job.util.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.kafka.support.SendResult;
import org.springframework.stereotype.Component;
import org.springframework.transaction.support.TransactionSynchronization;
import org.springframework.transaction.support.TransactionSynchronizationManager;
import org.springframework.util.concurrent.ListenableFutureCallback;

import javax.annotation.Resource;
import java.util.List;

/**
 * 保活任务消息发送业务类
 * 专门处理保活任务相关的Kafka消息发送
 * 支持事务感知的消息发送机制
 *
 * <AUTHOR>
 * @date 2025/8/15
 */
@Slf4j
@Component
public class KeepAliveMessageBiz {

    @Resource(name = "aliKafkaTemplate")
    private KafkaTemplate<String, Object> aliKafkaTemplate;

    /**
     * Kafka主题名称
     */
    private static final String KEEP_ALIVE_TOPIC = "events_cua_contract_keepalive";

    /**
     * 发送保活任务消息（事务感知）
     * 如果当前在事务中，则等待事务提交后再发送消息
     * 如果不在事务中，则立即发送消息
     *
     * @param taskDO 保活任务
     */
    public void sendKeepAliveMessage(ProviderParamsKeepaliveTaskDO taskDO) {
        if (taskDO == null) {
            log.warn("保活任务为空，跳过消息发送");
            return;
        }

        if (isInTransaction()) {
            // 在事务中，注册事务同步器，等待事务提交后发送
            sendMessageAfterTransactionCommit(taskDO);
        } else {
            // 不在事务中，立即发送
            sendMessageImmediately(taskDO);
        }
    }

    public void batchSendKeepAliveMessage(List<ProviderParamsKeepaliveTaskDO> taskDOs) {
        if (WosaiCollectionUtils.isEmpty(taskDOs)) {
            log.warn("保活任务为空，跳过消息发送");
            return;
        }

        if (isInTransaction()) {
            // 在事务中，注册事务同步器，等待事务提交后发送
            for (ProviderParamsKeepaliveTaskDO taskDO : taskDOs) {
                sendMessageAfterTransactionCommit(taskDO);
            }
        } else {
            // 不在事务中，立即发送
            for (ProviderParamsKeepaliveTaskDO taskDO : taskDOs) {
                sendMessageImmediately(taskDO);
            }
        }
    }

    /**
     * 检查当前是否在事务中
     *
     * @return true-在事务中，false-不在事务中
     */
    private boolean isInTransaction() {
        return TransactionSynchronizationManager.isActualTransactionActive();
    }

    /**
     * 在事务提交后发送单个消息
     *
     * @param taskDO 保活任务
     */
    private void sendMessageAfterTransactionCommit(ProviderParamsKeepaliveTaskDO taskDO) {
        TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
            @Override
            public void afterCommit() {
                try {
                    sendMessageImmediately(taskDO);
                } catch (Exception e) {
                    log.error("事务提交后发送保活消息失败，任务: {}", JSON.toJSONString(taskDO), e);
                }
            }

            @Override
            public void afterCompletion(int status) {
                if (status == STATUS_ROLLED_BACK) {
                    log.info("事务回滚，取消发送保活消息: {}", JSON.toJSONString(taskDO));
                }
            }
        });
    }

    /**
     * 立即发送单个消息
     *
     * @param taskDO 保活任务
     */
    private void sendMessageImmediately(ProviderParamsKeepaliveTaskDO taskDO) {
        MerchantKeepAliveParams merchantKeepAliveParams = new MerchantKeepAliveParams();
        merchantKeepAliveParams.setMerchantSn(taskDO.getMerchantSn());
        merchantKeepAliveParams.setProvider(taskDO.getProvider());
        merchantKeepAliveParams.setPayway(taskDO.getPayway());
        merchantKeepAliveParams.setTaskId(String.valueOf(taskDO.getTaskId()));
        merchantKeepAliveParams.setStatus(taskDO.getStatus());
        merchantKeepAliveParams.setKeepAliveStartDate(DateUtil.DATE_FORMATTER.format(taskDO.getStartDate()));
        merchantKeepAliveParams.setKeepAliveEndDate(DateUtil.DATE_FORMATTER.format(taskDO.getEndDate()));
        if (ProviderParamsKeepaliveTaskStatusEnum.ACTIVE.getValue().equals(taskDO.getStatus())) {
            merchantKeepAliveParams.setParams(taskDO.getParamsSnapshot());
        }
        log.info("发送保活状态变更消息: {}", merchantKeepAliveParams);
        aliKafkaTemplate.send(KEEP_ALIVE_TOPIC, merchantKeepAliveParams);
    }
}
