package com.wosai.upay.job.refactor.model.enums;

import com.shouqianba.cua.annotation.ITextValueEnum;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2025/8/14
 */
@Getter
public enum MerchantKeepAliveConfigStatusEnum implements ITextValueEnum<Integer> {
    CLOSE(0, "关闭"),
    OPEN(1, "开启");

    private final String text;
    private final Integer value;

    MerchantKeepAliveConfigStatusEnum(Integer value, String text) {
        this.value = value;
        this.text = text;
    }
}
