package com.wosai.upay.job.biz.keepalive;

import com.wosai.common.utils.WosaiCollectionUtils;
import com.wosai.upay.job.refactor.model.entity.MerchantProviderParamsDO;
import com.wosai.upay.job.refactor.model.enums.AuthStatusEnum;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 保活参数选择器工具类
 * 负责从交易参数中选择最优的保活参数
 *
 * <AUTHOR>
 * @date 2025/8/15
 */
public class KeepAliveParamsSelector {

    /**
     * 从所有参数中选择保活参数
     * 按 Provider 顺序处理，最多选择2种 Provider，每种 Provider 的每种 Payway 选择最优参数
     *
     * @param allParams                 所有交易参数（已按创建时间倒序排序）
     * @param keepAliveProviders        需要保活的 Provider 列表（按优先级排序）
     * @param paywayList                支付方式列表
     * @param maxKeepAliveProvidersSize 最大 provider数量
     * @return 筛选后的保活参数列表
     */
    public static List<MerchantProviderParamsDO> selectKeepAliveParams(List<MerchantProviderParamsDO> allParams,
                                                                       List<Integer> keepAliveProviders,
                                                                       List<Integer> paywayList, Integer maxKeepAliveProvidersSize) {
        if (WosaiCollectionUtils.isEmpty(allParams) || WosaiCollectionUtils.isEmpty(keepAliveProviders) || WosaiCollectionUtils.isEmpty(paywayList)) {
            return new ArrayList<>();
        }

        // 按 Provider 分组
        Map<Integer, List<MerchantProviderParamsDO>> paramsByProvider = allParams.stream()
                .collect(Collectors.groupingBy(MerchantProviderParamsDO::getProvider));

        List<MerchantProviderParamsDO> result = new ArrayList<>();
        int processedProviderCount = 0;

        // 按 Provider 优先级顺序处理
        for (Integer provider : keepAliveProviders) {
            List<MerchantProviderParamsDO> providerParams = paramsByProvider.get(provider);
            if (WosaiCollectionUtils.isEmpty(providerParams)) {
                continue;
            }

            List<MerchantProviderParamsDO> selectedParams = selectBestParamsForProvider(providerParams, paywayList);
            if (WosaiCollectionUtils.isNotEmpty(selectedParams)) {
                result.addAll(selectedParams);
                processedProviderCount++;

                // 最多处理指定数量的 Provider
                if (processedProviderCount >= maxKeepAliveProvidersSize) {
                    break;
                }
            }
        }

        return result;
    }

    /**
     * 为指定 Provider 的所有 Payway 选择最优参数
     *
     * @param providerParams 指定 Provider 的所有参数
     * @param paywayList     支付方式列表
     * @return 该 Provider 的最优参数列表
     */
    private static List<MerchantProviderParamsDO> selectBestParamsForProvider(List<MerchantProviderParamsDO> providerParams,
                                                                              List<Integer> paywayList) {
        List<MerchantProviderParamsDO> result = new ArrayList<>();

        // 按 Payway 分组
        Map<Integer, List<MerchantProviderParamsDO>> paramsByPayway = providerParams.stream()
                .collect(Collectors.groupingBy(MerchantProviderParamsDO::getPayway));

        // 为每种 Payway 选择最优参数
        for (Integer payway : paywayList) {
            List<MerchantProviderParamsDO> paywayParams = paramsByPayway.get(payway);
            if (WosaiCollectionUtils.isEmpty(paywayParams)) {
                continue;
            }

            Optional<MerchantProviderParamsDO> bestParam = selectBestParamForPayway(paywayParams);
            bestParam.ifPresent(result::add);
        }

        return result;
    }

    /**
     * 为指定 Payway 选择最优参数
     * 优先选择已授权的参数，如果没有已授权的参数，则选择创建时间最新的参数
     *
     * @param paywayParams 指定 Payway 的所有参数（已按创建时间倒序排序）
     * @return 最优参数
     */
    private static Optional<MerchantProviderParamsDO> selectBestParamForPayway(List<MerchantProviderParamsDO> paywayParams) {
        if (WosaiCollectionUtils.isEmpty(paywayParams)) {
            return Optional.empty();
        }

        // 优先选择已授权的参数（在已授权的参数中，由于原列表已排序，第一个就是最新的）
        Optional<MerchantProviderParamsDO> authorizedParam = paywayParams.stream()
                .filter(param -> Objects.equals(AuthStatusEnum.YES.getValue(), param.getAuthStatus()))
                .findFirst();

        // 如果有已授权的参数则返回，否则返回最新的参数（列表第一个）
        return authorizedParam.isPresent() ? authorizedParam : Optional.of(paywayParams.get(0));
    }
}
