package com.wosai.upay.job.refactor.dao;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.wosai.upay.job.refactor.mapper.SequenceDynamicMapper;
import com.wosai.upay.job.refactor.model.entity.SequenceDO;
import org.apache.ibatis.session.SqlSessionFactory;
import org.springframework.stereotype.Repository;

import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2025/9/2
 */
@Repository
public class SequenceDAO extends AbstractBaseDAO<SequenceDO, SequenceDynamicMapper> {

    protected SequenceDAO(SqlSessionFactory sqlSessionFactory, SequenceDynamicMapper entityMapper) {
        super(sqlSessionFactory, entityMapper);
    }

    public Optional<SequenceDO> selectForUpdate(String namespace) {
        LambdaQueryWrapper<SequenceDO> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(SequenceDO::getNamespace, namespace);
        lambdaQueryWrapper.last("for update");
        return Optional.ofNullable(entityMapper.selectOne(lambdaQueryWrapper));
    }

    public void update(SequenceDO sequence) {
        entityMapper.updateById(sequence);
    }
}
