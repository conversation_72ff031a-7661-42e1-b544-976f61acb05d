package com.wosai.upay.job.biz.keepalive.validation.rule.impl;

import com.wosai.common.utils.WosaiCollectionUtils;
import com.wosai.common.utils.WosaiMapUtils;
import com.wosai.upay.job.adapter.apollo.model.KeepAliveCheckRuleConfig;
import com.wosai.upay.job.biz.keepalive.validation.enums.KeepAliveCheckRuleTypeEnum;
import com.wosai.upay.job.biz.keepalive.validation.model.KeepAliveCheckRuleResult;
import com.wosai.upay.job.biz.keepalive.validation.model.KeepAliveValidateContext;
import com.wosai.upay.job.biz.keepalive.validation.rule.AbstractKeepAliveCheckRule;
import com.wosai.upay.job.refactor.dao.SubMchIdLastTradeTimeDAO;
import com.wosai.upay.job.refactor.model.entity.SubMchIdLastTradeTimeDO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.Instant;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.temporal.ChronoUnit;
import java.util.Comparator;
import java.util.List;
import java.util.Optional;

/**
 * 商户交易活跃
 *
 * <AUTHOR>
 * @date 2025/8/28
 */
@Component
public class PayActiveRule extends AbstractKeepAliveCheckRule {

    @Autowired
    private SubMchIdLastTradeTimeDAO subMchIdLastTradeTimeDAO;

    @Override
    protected KeepAliveCheckRuleResult doExecute(KeepAliveValidateContext context, KeepAliveCheckRuleConfig ruleConfig) {
        try {
            List<SubMchIdLastTradeTimeDO> subMchIdLastTradeTimeDOS = subMchIdLastTradeTimeDAO.selectByMerchantSn(context.getMerchantSn());
            if (WosaiCollectionUtils.isEmpty(subMchIdLastTradeTimeDOS)) {
                return createFailureResult(
                        "商户交易非活跃，不允许执行", "PAY_ACTIVE_BLOCKED");
            }

            // 获取配置的最大天数，默认为30天
            int maxDay = WosaiMapUtils.getIntValue(ruleConfig.getParams(), "maxDay", 90);

            // 按照trade_time排序，取交易时间最近的一个时间
            Optional<SubMchIdLastTradeTimeDO> latestTradeOptional = subMchIdLastTradeTimeDOS.stream()
                    .max(Comparator.comparing(SubMchIdLastTradeTimeDO::getTradeTime));

            if (!latestTradeOptional.isPresent()) {
                return createFailureResult(
                        "商户交易非活跃，不允许执行", "PAY_ACTIVE_BLOCKED");
            }

            SubMchIdLastTradeTimeDO latestTrade = latestTradeOptional.get();

            // 计算最近交易时间和今天的差值
            LocalDate latestTradeDate = Instant.ofEpochMilli(latestTrade.getTradeTime())
                    .atZone(ZoneId.systemDefault())
                    .toLocalDate();
            LocalDate today = LocalDate.now();
            long daysBetween = ChronoUnit.DAYS.between(latestTradeDate, today);

            // 判断是否活跃
            if (daysBetween <= maxDay) {
                return createSuccessResult(String.format("商户交易活跃正常，最近交易时间: %s，距今 %d 天，通过检查",
                        latestTradeDate, daysBetween));
            } else {
                return createFailureResult(
                        String.format("商户交易非活跃，最近交易时间: %s，距今 %d 天，超过配置的 %d 天，不允许执行",
                                latestTradeDate, daysBetween, maxDay),
                        "PAY_ACTIVE_BLOCKED");
            }

        } catch (Exception e) {
            logger.error("执行商户交易活跃规则检查时发生异常，商户SN: {}", context.getMerchantSn(), e);
            return createFailureResult("规则执行异常: " + e.getMessage(), "EXECUTION_ERROR");
        }
    }

    @Override
    public KeepAliveCheckRuleTypeEnum getRuleType() {
        return KeepAliveCheckRuleTypeEnum.PAY_ACTIVE;
    }
}
