package com.wosai.upay.job.refactor.model.enums;


import com.shouqianba.cua.annotation.ITextValueEnum;
import lombok.Getter;

/**
 * 参数保活任务类型枚举
 *
 * <AUTHOR>
@Getter
public enum ProviderParamsKeepaliveTaskTypeEnum implements ITextValueEnum<Integer> {

    /**
     * 自动
     */
    AUTO(1, "系统自动保活"),

    /**
     * 人工
     */
    MANUAL(2, "人工保活");

    ProviderParamsKeepaliveTaskTypeEnum(Integer value, String text) {
        this.value = value;
        this.text = text;
    }
    private final Integer value;
    private final String text;
}