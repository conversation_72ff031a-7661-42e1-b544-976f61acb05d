package com.wosai.upay.job.biz.keepalive.validation.enums;

import com.wosai.upay.merchant.contract.exception.ContractBizException;
import lombok.Getter;

/**
 * 校验场景枚举
 */
@Getter
public enum KeepAliveValidationScenarioEnum {
    
    /**
     * 开通圈选
     */
    OPEN_CIRCLE("OPEN_CIRCLE", "开通圈选"),
    
    /**
     * 预执行圈选
     */
    PRE_EXECUTE_CIRCLE("PRE_EXECUTE_CIRCLE", "预执行圈选"),
    
    /**
     * 执行策略
     */
    EXECUTE_STRATEGY("EXECUTE_STRATEGY", "执行策略");
    
    private final String code;
    private final String description;
    
    KeepAliveValidationScenarioEnum(String code, String description) {
        this.code = code;
        this.description = description;
    }
    
    public static KeepAliveValidationScenarioEnum fromCode(String code) {
        for (KeepAliveValidationScenarioEnum scenario : values()) {
            if (scenario.getCode().equals(code)) {
                return scenario;
            }
        }
        throw new ContractBizException("Unknown scenario code: " + code);
    }
}