package com.wosai.upay.job.service;

import com.alibaba.fastjson.JSON;
import com.googlecode.jsonrpc4j.spring.AutoJsonRpcServiceImpl;
import com.shouqianba.cua.enums.core.BankAccountTypeEnum;
import com.shouqianba.cua.enums.core.BusinessLicenseTypeEnum;
import com.wosai.common.utils.WosaiMapUtils;
import com.wosai.data.util.CollectionUtil;
import com.wosai.upay.common.dao.DaoConstants;
import com.wosai.upay.core.model.MerchantBankAccount;
import com.wosai.upay.core.model.MerchantBusinessLicence;
import com.wosai.upay.core.model.TransactionParam;
import com.wosai.upay.core.service.MerchantService;
import com.wosai.upay.core.service.SupportService;
import com.wosai.upay.core.service.TradeConfigService;
import com.wosai.upay.job.biz.paramContext.ParamContextBiz;
import com.wosai.upay.job.model.umb.UmbMerchantAcquirerInfo;
import com.wosai.upay.job.refactor.dao.MerchantAcquirerInfoDAO;
import com.wosai.upay.job.refactor.dao.MerchantProviderParamsDAO;
import com.wosai.upay.job.refactor.model.entity.MerchantAcquirerInfoDO;
import com.wosai.upay.job.refactor.model.entity.MerchantProviderParamsDO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

@Slf4j
@AutoJsonRpcServiceImpl
@Component
@RequiredArgsConstructor
public class UmbDataCleanServiceImpl implements UmbDataCleanService {
    private final MerchantAcquirerInfoDAO merchantAcquirerInfoDAO;
    private final MerchantProviderParamsDAO merchantProviderParamsDAO;
    private final TradeConfigService tradeConfigService;
    private final SupportService supportService;
    private final ParamContextBiz paramContextBiz;
    private final MerchantService merchantService;

    @Override
    public Map<String, Object> setTradeConfigForUmbShareProfit(List<String> snList, boolean shareProfit) {
        long startTime = System.currentTimeMillis();
        int size;
        if (snList.isEmpty()) {
            List<MerchantAcquirerInfoDO> infoDOS = merchantAcquirerInfoDAO.getByAcquirerAndCtime("umb", "2025-06-18 23:20:15.390");
            size = infoDOS.size();
            for (MerchantAcquirerInfoDO infoDO : infoDOS) {
                String merchantSn = "";
                try {
                    //只处理成功进件的商户
                    String merchantInfo = infoDO.getMerchantInfo();
                    String statusInStr = JSON.parseObject(merchantInfo, UmbMerchantAcquirerInfo.class).getDetailed_status();
                    if (!"4".equals(statusInStr)) {
                        continue;
                    }
                    merchantSn = infoDO.getMerchantSn();
                    setConfigParam(merchantSn);
                } catch (Exception e) {
                    log.error("清洗中投分账交易参数错误. {} ", merchantSn, e);
                }
            }
        } else {
            size = snList.size();
            for (String merchantSn : snList) {
                try {
                    setConfigParamDirect(merchantSn, shareProfit);
                } catch (Exception e) {
                    log.error("清洗中投分账交易参数错误. {} ", merchantSn, e);
                }
            }
        }
        long duration = System.currentTimeMillis() - startTime;
        log.info("中投洗数据{}条耗时：{} 秒", size, duration / 1000);
        return Collections.emptyMap();
    }

    private void setConfigParamDirect(String merchantSn, boolean shareProfit) {
        Optional<MerchantProviderParamsDO> params = merchantProviderParamsDAO.getMerchantProviderParamsByProviderAndPayway(merchantSn, 1050, 0);
        Map merchantBySn = merchantService.getMerchantBySn(merchantSn);
        doSet(merchantSn, WosaiMapUtils.getString(merchantBySn, DaoConstants.ID), params, shareProfit);
    }

    private void setConfigParam(String merchantSn) {
        Optional<MerchantProviderParamsDO> params = merchantProviderParamsDAO.getMerchantProviderParamsByProviderAndPayway(merchantSn, 1050, 0);
        if (params.isPresent()) {
            // 判断是否分账.
            Map<String, Object> contextParam = paramContextBiz.getParamContextByMerchantSn(merchantSn, null, false);
            Map<String, Object> merchant = (Map) contextParam.get("merchant");
            Map<String, Object> bankAccount = (Map) contextParam.get("bankAccount");
            Map<String, Object> businessLicense = (Map) contextParam.get("merchantBusinessLicense");
            //只有公司使用对私卡  和 个体但非法人 的情况,需要进行分润.
            //这里只允许同名换卡.
            boolean shareProfit = false;
            //个体但结算卡非法人
            if (BusinessLicenseTypeEnum.isIndividual((Integer) businessLicense.get(MerchantBusinessLicence.TYPE))) {
                boolean bankCardIsPrivateAndBankAccountHolderNotEqualBusinessLicenseLegalPerson = BankAccountTypeEnum.isPersonal((Integer) bankAccount.get(MerchantBankAccount.TYPE)) && !Objects.equals(bankAccount.get(MerchantBankAccount.HOLDER), businessLicense.get(MerchantBusinessLicence.LEGAL_PERSON_NAME));
                if (bankCardIsPrivateAndBankAccountHolderNotEqualBusinessLicenseLegalPerson) {
                    shareProfit = true;
                }
            }
            //公司使用对私卡
            if (BusinessLicenseTypeEnum.isEnterprise((Integer) businessLicense.get(MerchantBusinessLicence.TYPE))) {
                if (BankAccountTypeEnum.isPersonal((Integer) bankAccount.get(MerchantBankAccount.TYPE))) {
                    shareProfit = true;
                }
            }
            if (shareProfit) {
                doSet(merchantSn, WosaiMapUtils.getString(merchant, DaoConstants.ID), params, shareProfit);
            }
        }
    }

    private void doSet(String merchantSn, String merchantId, Optional<MerchantProviderParamsDO> params, boolean shareProfit) {
        tradeConfigService.updateZTKXTradeParams(
                merchantId,
                CollectionUtil.hashMap(
                        TransactionParam.LIQUIDATION_NEXT_DAY, !shareProfit,
                        TransactionParam.PLATFORM_MCH_ID, params.get().getChannelNo(),
                        TransactionParam.PROVIDER_MCH_ID, params.get().getPayMerchantId()
                )
        );
        supportService.removeCachedParams(merchantSn);
        String type = shareProfit ? "直清" : "间清";
        log.info("商户. {} 设置{}成功", merchantSn, type);
    }
}
