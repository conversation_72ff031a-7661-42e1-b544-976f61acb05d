package com.wosai.upay.job.refactor.dao;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.wosai.common.utils.WosaiCollectionUtils;
import com.wosai.common.utils.WosaiStringUtils;
import com.wosai.upay.job.refactor.mapper.AcquirerAccountChangeRuleMapper;
import com.wosai.upay.job.refactor.model.entity.AcquirerAccountChangeRuleDO;
import com.wosai.upay.merchant.contract.exception.ContractBizException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.session.SqlSessionFactory;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.TimeUnit;

/**
 * 收单机构账户变更规则DAO
 * 
 * <AUTHOR>
 * @since 2025-01-22
 */
@Repository
@Slf4j
public class AcquirerAccountChangeRuleDAO extends AbstractBaseDAO<AcquirerAccountChangeRuleDO, AcquirerAccountChangeRuleMapper> {

    /**
     * 规则缓存
     */
    private static final Cache<String, List<AcquirerAccountChangeRuleDO>> RULES_CACHE = CacheBuilder.newBuilder()
            .maximumSize(200)
            .expireAfterWrite(5, TimeUnit.MINUTES)
            .expireAfterAccess(10, TimeUnit.MINUTES)
            .build();

    /**
     * 单个规则缓存
     */
    private static final Cache<String, AcquirerAccountChangeRuleDO> SINGLE_RULE_CACHE = CacheBuilder.newBuilder()
            .maximumSize(200)
            .expireAfterWrite(5, TimeUnit.MINUTES)
            .expireAfterAccess(10, TimeUnit.MINUTES)
            .recordStats()
            .build();

    public AcquirerAccountChangeRuleDAO(SqlSessionFactory sqlSessionFactory, AcquirerAccountChangeRuleMapper entityMapper) {
        super(sqlSessionFactory, entityMapper);
    }

    /**
     * 根据收单机构查询规则（使用缓存）
     */
    public Optional<AcquirerAccountChangeRuleDO> getByAcquirer(String acquirer) {
        if (StringUtils.isEmpty(acquirer)) {
            throw new ContractBizException("收单机构不可以为空");
        }
        try {
            String cacheKey = "single_" + acquirer;
            AcquirerAccountChangeRuleDO cachedRule = SINGLE_RULE_CACHE.get(cacheKey, () -> {
                LambdaQueryWrapper<AcquirerAccountChangeRuleDO> wrapper = new LambdaQueryWrapper<>();
                wrapper.eq(AcquirerAccountChangeRuleDO::getAcquirer, acquirer)
                       .orderByDesc(AcquirerAccountChangeRuleDO::getId);
                
                Optional<AcquirerAccountChangeRuleDO> result = selectOne(wrapper);
                return result.orElse(null);
            });
            
            return Optional.ofNullable(cachedRule);
            
        } catch (Exception e) {
            log.error("getByAcquirer cache error, acquirer: {}", acquirer, e);
            LambdaQueryWrapper<AcquirerAccountChangeRuleDO> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(AcquirerAccountChangeRuleDO::getAcquirer, acquirer)
                   .orderByDesc(AcquirerAccountChangeRuleDO::getId)
                   .last("LIMIT 1");
            
            return selectOne(wrapper);
        }
    }

    /**
     * 根据收单机构查询所有规则
     */
    public List<AcquirerAccountChangeRuleDO> findByAcquirer(String acquirer) {
        if (WosaiStringUtils.isEmpty(acquirer)) {
            return new ArrayList<>();
        }
        
        try {
            List<AcquirerAccountChangeRuleDO> cachedRules = RULES_CACHE.get(acquirer, () -> {
                LambdaQueryWrapper<AcquirerAccountChangeRuleDO> wrapper = new LambdaQueryWrapper<>();
                wrapper.eq(AcquirerAccountChangeRuleDO::getAcquirer, acquirer)
                       .orderByDesc(AcquirerAccountChangeRuleDO::getCtime);
                
                List<AcquirerAccountChangeRuleDO> rules = entityMapper.selectList(wrapper);
                log.debug("Loaded {} rules from database for acquirer: {}", 
                    rules != null ? rules.size() : 0, acquirer);
                return rules != null ? rules : new ArrayList<>();
            });
            
            log.trace("Retrieved {} rules from cache for acquirer: {}", 
                cachedRules.size(), acquirer);
            return cachedRules;
            
        } catch (Exception e) {
            log.error("findByAcquirer cache error, acquirer: {}", acquirer, e);
            LambdaQueryWrapper<AcquirerAccountChangeRuleDO> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(AcquirerAccountChangeRuleDO::getAcquirer, acquirer)
                   .orderByDesc(AcquirerAccountChangeRuleDO::getCtime);
            
            List<AcquirerAccountChangeRuleDO> rules = entityMapper.selectList(wrapper);
            return rules != null ? rules : new ArrayList<>();
        }
    }

    /**
     * 查询所有有效规则
     */
    public List<AcquirerAccountChangeRuleDO> listAllRules() {
        LambdaQueryWrapper<AcquirerAccountChangeRuleDO> wrapper = new LambdaQueryWrapper<>();
        wrapper.orderByDesc(AcquirerAccountChangeRuleDO::getCtime);
        
        return entityMapper.selectList(wrapper);
    }

    /**
     * 根据收单机构和规则名称查询规则
     */
    public Optional<AcquirerAccountChangeRuleDO> findByAcquirerAndRuleName(String acquirer, String ruleName) {
        if (WosaiStringUtils.isEmpty(acquirer) || WosaiStringUtils.isEmpty(ruleName)) {
            throw new ContractBizException("acquirer or ruleName is null");
        }
        
        LambdaQueryWrapper<AcquirerAccountChangeRuleDO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(AcquirerAccountChangeRuleDO::getAcquirer, acquirer)
               .eq(AcquirerAccountChangeRuleDO::getRuleName, ruleName)
               .orderByDesc(AcquirerAccountChangeRuleDO::getId)
               .last("LIMIT 1");
        
        return selectOne(wrapper);
    }

    /**
     * 根据收单机构查询规则列表（委托给findByAcquirer，复用缓存）
     */
    public List<AcquirerAccountChangeRuleDO> listByAcquirer(String acquirer) {
        return findByAcquirer(acquirer);
    }


    /**
     * 清空指定收单机构的缓存
     * 
     * @param acquirer 收单机构
     */
    public void evictCache(String acquirer) {
        if (WosaiStringUtils.isEmpty(acquirer)) {
            return;
        }
        
        RULES_CACHE.invalidate(acquirer);
        SINGLE_RULE_CACHE.invalidate("single_" + acquirer);
        log.info("Evicted cache for acquirer: {}", acquirer);
    }

    /**
     * 清空所有缓存
     * 
     * 在规则批量更新时使用
     */
    public void evictAllCache() {
        RULES_CACHE.invalidateAll();
        SINGLE_RULE_CACHE.invalidateAll();
        log.info("Evicted all rules cache");
    }





}