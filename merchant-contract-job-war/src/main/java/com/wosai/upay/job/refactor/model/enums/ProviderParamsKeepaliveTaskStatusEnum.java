package com.wosai.upay.job.refactor.model.enums;


import com.shouqianba.cua.annotation.ITextValueEnum;
import lombok.Getter;

/**
 * 参数保活任务状态枚举
 *
 * <AUTHOR>
@Getter
public enum ProviderParamsKeepaliveTaskStatusEnum implements ITextValueEnum<Integer> {

    /**
     * 待保活
     */
    PENDING(1, "待保活"),

    /**
     * 保活中
     */
    ACTIVE(2, "保活中"),

    /**
     * 成功
     */
    SUCCESS(3, "成功"),

    /**
     * 失败
     */
    FAILED(4, "失败"),

    /**
     * 取消
     */
    CANCELLED(5, "取消");

    ProviderParamsKeepaliveTaskStatusEnum(Integer value, String text) {
        this.value = value;
        this.text = text;
    }
    private final Integer value;
    private final String text;
}