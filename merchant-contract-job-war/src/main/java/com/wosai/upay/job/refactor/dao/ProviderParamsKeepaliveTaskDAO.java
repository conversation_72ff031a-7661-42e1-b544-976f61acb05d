package com.wosai.upay.job.refactor.dao;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.wosai.common.utils.WosaiStringUtils;
import com.wosai.upay.job.refactor.mapper.ProviderParamsKeepaliveTaskDynamicMapper;
import com.wosai.upay.job.refactor.model.bo.KeepAliveTaskResultBO;
import com.wosai.upay.job.refactor.model.entity.ProviderParamsKeepaliveTaskDO;
import com.wosai.upay.job.refactor.model.enums.ProviderParamsKeepaliveTaskStatusEnum;
import com.wosai.upay.merchant.contract.exception.ContractBizException;
import org.apache.ibatis.session.SqlSessionFactory;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;

/**
 * 参数保活任务表数据库访问层 {@link ProviderParamsKeepaliveTaskDO}
 * 对ProviderParamsKeepaliveTaskDynamicMapper层做出简单封装 {@link ProviderParamsKeepaliveTaskDynamicMapper}
 *
 * <AUTHOR>
@Repository
public class ProviderParamsKeepaliveTaskDAO extends AbstractBaseDAO<ProviderParamsKeepaliveTaskDO, ProviderParamsKeepaliveTaskDynamicMapper> {

    public ProviderParamsKeepaliveTaskDAO(SqlSessionFactory sqlSessionFactory, ProviderParamsKeepaliveTaskDynamicMapper entityMapper) {
        super(sqlSessionFactory, entityMapper);
    }

    /**
     * 查询待保活且保活日期为指定日期的任务
     *
     * @param startDate 保活开始日期
     * @return 参数保活任务记录列表
     */
    public List<ProviderParamsKeepaliveTaskDO> selectPendingTasksByStartDate(LocalDate startDate, long limit) {
        LambdaQueryWrapper<ProviderParamsKeepaliveTaskDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProviderParamsKeepaliveTaskDO::getStatus, ProviderParamsKeepaliveTaskStatusEnum.PENDING.getValue()) // 1:待保活
                .eq(ProviderParamsKeepaliveTaskDO::getStartDate, startDate)
                .orderByAsc(ProviderParamsKeepaliveTaskDO::getId)
                .last("limit " + limit);
        return entityMapper.selectList(queryWrapper);
    }

    public List<ProviderParamsKeepaliveTaskDO> selectActiveTasksByEndDate(LocalDate endDate, long limit) {
        LambdaQueryWrapper<ProviderParamsKeepaliveTaskDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProviderParamsKeepaliveTaskDO::getStatus, ProviderParamsKeepaliveTaskStatusEnum.ACTIVE.getValue()) // 2:保活中
                .eq(ProviderParamsKeepaliveTaskDO::getEndDate, endDate)
                .orderByAsc(ProviderParamsKeepaliveTaskDO::getId)
                .last("limit " + limit);
        return entityMapper.selectList(queryWrapper);
    }

    /**
     * 更新保活任务
     *
     * @return 影响行数
     */
    public int update(ProviderParamsKeepaliveTaskDO taskDO) {
        int affectedRows = entityMapper.updateById(taskDO);
        if (affectedRows < 1) {
            throw new ContractBizException("更新保活任务失败，请重试");
        }
        return affectedRows;
    }

    /**
     * 查询保活中且保活结束时间小于指定时间的任务
     *
     * @param merchantSn 商户号
     * @return 参数保活任务记录列表
     */
    public List<ProviderParamsKeepaliveTaskDO> selectNotCompletedTasksByMerchantSn(String merchantSn) {
        LambdaQueryWrapper<ProviderParamsKeepaliveTaskDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProviderParamsKeepaliveTaskDO::getMerchantSn, merchantSn)
                .in(ProviderParamsKeepaliveTaskDO::getStatus, Arrays.asList(ProviderParamsKeepaliveTaskStatusEnum.PENDING.getValue(), ProviderParamsKeepaliveTaskStatusEnum.ACTIVE.getValue()));
        return entityMapper.selectList(queryWrapper);
    }

    public Optional<ProviderParamsKeepaliveTaskDO> selectByTaskId(Long taskId) {
        LambdaQueryWrapper<ProviderParamsKeepaliveTaskDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProviderParamsKeepaliveTaskDO::getTaskId, taskId);
        return selectOne(queryWrapper);
    }

    public Page<ProviderParamsKeepaliveTaskDO> queryKeepAliveTasks(String merchantSn, String subMchId, int pageNum, int pageSize) {
        LambdaQueryWrapper<ProviderParamsKeepaliveTaskDO> queryWrapper = new LambdaQueryWrapper<>();
        if (WosaiStringUtils.isNotBlank(merchantSn)) {
            queryWrapper.eq(ProviderParamsKeepaliveTaskDO::getMerchantSn, merchantSn);
        }
        if (WosaiStringUtils.isNotBlank(subMchId)) {
            queryWrapper.eq(ProviderParamsKeepaliveTaskDO::getSubMchId, subMchId);
        }
        return PageHelper.startPage(pageNum, pageSize).doSelectPage(() -> entityMapper.selectList(queryWrapper));
    }

    /**
     * 启动保活任务（充血模型方法）
     *
     * @param taskDO         保活任务对象
     * @param paramsSnapshot 参数快照
     */
    public void startKeepAliveTask(ProviderParamsKeepaliveTaskDO taskDO, String paramsSnapshot, KeepAliveTaskResultBO resultBO) {
        taskDO.startKeepAlive(paramsSnapshot, resultBO);
        update(taskDO);
    }

    /**
     * 完成保活任务（充血模型方法）
     *
     * @param taskDO    保活任务对象
     * @param resultBO  结果信息
     * @param payOrder  支付订单号
     * @param payAmount 支付金额
     * @param payTime   支付时间
     */
    public void completeKeepAliveTask(ProviderParamsKeepaliveTaskDO taskDO,
                                      String payOrder, Long payAmount, Long payTime, KeepAliveTaskResultBO resultBO) {
        taskDO.completeSuccessfully(payOrder, payAmount, payTime, resultBO);
        update(taskDO);
    }

    /**
     * 失败保活任务（充血模型方法）
     *
     * @param taskDO   保活任务对象
     * @param resultBO 失败原因
     */
    public void failKeepAliveTask(ProviderParamsKeepaliveTaskDO taskDO, KeepAliveTaskResultBO resultBO) {
        taskDO.fail(resultBO);
        update(taskDO);
    }

    /**
     * 取消保活任务（充血模型方法）
     *
     * @param taskDO   保活任务对象
     * @param resultBO 取消原因
     */
    public void cancelKeepAliveTask(ProviderParamsKeepaliveTaskDO taskDO, KeepAliveTaskResultBO resultBO) {
        taskDO.cancel(resultBO);
        update(taskDO);
    }

    /**
     * 批量取消保活任务（充血模型方法）
     *
     * @param taskList 保活任务列表
     * @param resultBO 取消原因
     */
    @Transactional(rollbackFor = Exception.class)
    public Integer batchCancelKeepAliveTasks(List<ProviderParamsKeepaliveTaskDO> taskList, KeepAliveTaskResultBO resultBO) {
        for (ProviderParamsKeepaliveTaskDO taskDO : taskList) {
            taskDO.cancel(resultBO);
        }
        Integer affectedRows = batchUpdateByIdSelective(taskList);
        if (affectedRows < taskList.size()) {
            throw new ContractBizException("批量取消保活任务失败，请重试");
        }
        return affectedRows;
    }
}