package com.wosai.upay.job.refactor.model.bo;

import com.wosai.common.utils.WosaiStringUtils;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.time.LocalDateTime;

/**
 * 保活任务结果BO
 * 用于存储provider_params_keepalive_task表中result字段的结构化数据
 *
 * <AUTHOR>
 * @date 2025/8/15
 */
@Data
public class KeepAliveTaskResultBO {

    /**
     * 操作来源（服务或平台）
     */
    private String source;

    /**
     * 操作类型（API、SCHEDULE等）
     */
    private String operationType;

    /**
     * 操作人
     */
    private String operator;

    /**
     * 备注
     */
    private String remark;

    /**
     * 操作类型枚举
     */
    public static class OperationTypes {
        public static final String API = "API";                 // API调用
        public static final String SCHEDULE = "SCHEDULE";       // 定时任务
        public static final String EVENT = "EVENT";             // 事件触发
    }

    /**
     * 标准化备注模板
     */
    public static class RemarkTemplates {

        /**
         * 手动创建任务
         */
        public static final String MANUAL_CREATE = "手动创建保活任务，操作人：%s，备注：%s";
        /**
         * 自动创建保活任务 监听事件
         */
        public static final String AUTO_CREATE = "自动创建保活任务，操作人：%s，备注：%s";
        /**
         * 保活任务执行中
         */
        public static final String EXECUTING = "保活任务执行中";

        /**
         * 任务执行成功
         */
        public static final String EXECUTE_SUCCESS = "保活任务执行成功，执行时间：%s";

        /**
         * 任务执行失败
         */
        public static final String EXECUTE_FAILED = "保活任务执行失败，失败原因：%s";

        /**
         * 任务被取消
         */
        public static final String TASK_CANCELLED = "保活任务被取消，取消原因：%s，操作人：%s";

        /**
         * 系统异常
         */
        public static final String SYSTEM_ERROR = "系统异常导致任务失败，异常信息：%s";
    }

    /**
     * 默认构造函数
     */
    public KeepAliveTaskResultBO() {
    }

    /**
     * 构造函数
     *
     * @param source        操作来源
     * @param operationType 操作类型
     * @param operator      操作人
     * @param remark        备注
     */
    public KeepAliveTaskResultBO(String source, String operationType, String operator, String remark) {
        this.source = source;
        this.operationType = operationType;
        this.operator = operator;
        this.remark = remark;
    }

    /**
     * 创建任务的结果 from api
     *
     * @param source   来源
     * @param operator 操作人
     * @param remark   备注
     * @return KeepAliveTaskResultBO
     */
    public static KeepAliveTaskResultBO createResultFromAPI(String source, String operator, String remark) {
        String formatRemark = formatRemark(RemarkTemplates.MANUAL_CREATE, operator, WosaiStringUtils.isNotEmpty(remark) ? remark : StringUtils.EMPTY);
        return new KeepAliveTaskResultBO(source, OperationTypes.API, operator, formatRemark);
    }

    /**
     * 创建任务的结果 from api
     *
     * @param operator 操作人
     * @param remark   备注
     * @return KeepAliveTaskResultBO
     */
    public static KeepAliveTaskResultBO createResultFromEvent(String operator, String remark) {
        String formatRemark = formatRemark(RemarkTemplates.AUTO_CREATE, operator, WosaiStringUtils.isNotEmpty(remark) ? remark : StringUtils.EMPTY);
        return new KeepAliveTaskResultBO("merchant-contract-job", OperationTypes.EVENT, operator, formatRemark);
    }

    /**
     * 创建任务取消的结果
     *
     * @param cancelReason 取消原因
     * @param operator     操作人
     * @return KeepAliveTaskResultBO
     */
    public static KeepAliveTaskResultBO createTaskCancelledResultFromAPI(String source, String operator, String cancelReason) {
        String remark = formatRemark(RemarkTemplates.TASK_CANCELLED, cancelReason, operator);
        return new KeepAliveTaskResultBO(source, OperationTypes.API, operator, remark);
    }

    /**
     * 创建任务执行成功的结果 from api
     *
     * @param source   操作来源
     * @param operator 操作人
     * @return KeepAliveTaskResultBO
     */
    public static KeepAliveTaskResultBO createExecuteSuccessResultFromAPI(String source, String operator) {
        String remark = formatRemark(RemarkTemplates.EXECUTE_SUCCESS, LocalDateTime.now().toString());
        return new KeepAliveTaskResultBO(source, OperationTypes.API, operator, remark);
    }

    /**
     * 创建任务执行失败的结果 from api
     *
     * @param source     操作来源
     * @param operator   操作人
     * @param failReason 失败原因
     * @return KeepAliveTaskResultBO
     */
    public static KeepAliveTaskResultBO createExecuteFailedFromAPI(String source, String operator, String failReason) {
        String remark = formatRemark(RemarkTemplates.EXECUTE_FAILED, failReason);
        return new KeepAliveTaskResultBO(source, OperationTypes.API, operator, remark);
    }

    /**
     * 创建任务执行失败的结果 from schedule
     *
     * @param operator   操作人
     * @param failReason 失败原因
     * @return KeepAliveTaskResultBO
     */
    public static KeepAliveTaskResultBO createExecuteFailedResultFromSchedule(String operator, String failReason) {
        String remark = formatRemark(RemarkTemplates.EXECUTE_FAILED, failReason);
        return new KeepAliveTaskResultBO("merchant-contract-job", OperationTypes.SCHEDULE, operator, remark);
    }

    /**
     * 创建任务取消的结果
     *
     * @param cancelReason 取消原因
     * @param operator     操作人
     * @return KeepAliveTaskResultBO
     */
    public static KeepAliveTaskResultBO createTaskCancelledResultFromSchedule(String operator, String cancelReason) {
        String remark = formatRemark(RemarkTemplates.TASK_CANCELLED, cancelReason, operator);
        return new KeepAliveTaskResultBO("merchant-contract-job", OperationTypes.SCHEDULE, operator, remark);
    }

    /**
     * 创建任务执行中的结果
     *
     * @param operator     操作人
     * @return KeepAliveTaskResultBO
     */
    public static KeepAliveTaskResultBO createTaskExecutingResultFromSchedule(String operator) {
        return new KeepAliveTaskResultBO("merchant-contract-job", OperationTypes.SCHEDULE, operator, RemarkTemplates.EXECUTING);
    }

    /**
     * 创建任务取消的结果
     *
     * @param cancelReason 取消原因
     * @param operator     操作人
     * @return KeepAliveTaskResultBO
     */
    public static KeepAliveTaskResultBO createTaskCancelledResult(String source, String operationType, String operator, String cancelReason) {
        String remark = formatRemark(RemarkTemplates.TASK_CANCELLED, cancelReason, operator);
        return new KeepAliveTaskResultBO(source, operationType, operator, remark);
    }

    /**
     * 使用模板创建备注
     *
     * @param template 备注模板
     * @param args     模板参数
     * @return 格式化后的备注
     */
    public static String formatRemark(String template, Object... args) {
        if (template == null || args == null || args.length == 0) {
            return template;
        }
        return String.format(template, args);
    }

    /**
     * 创建定时任务系统异常的结果
     *
     * @param errorMessage 异常信息
     * @return KeepAliveTaskResultBO
     */
    public static KeepAliveTaskResultBO createScheduleSystemErrorResult(String operator, String errorMessage) {
        String remark = formatRemark(RemarkTemplates.SYSTEM_ERROR, errorMessage);
        return new KeepAliveTaskResultBO("merchant-contract-job", OperationTypes.SCHEDULE, operator, remark);
    }

    @Override
    public String toString() {
        return "KeepAliveTaskResultBO{" +
                "source='" + source + '\'' +
                ", operationType='" + operationType + '\'' +
                ", operator='" + operator + '\'' +
                ", remark='" + remark + '\'' +
                '}';
    }
}
