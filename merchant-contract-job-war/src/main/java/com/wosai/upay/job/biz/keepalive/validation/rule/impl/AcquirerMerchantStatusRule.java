package com.wosai.upay.job.biz.keepalive.validation.rule.impl;

import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.shouqianba.cua.enums.core.PaywayEnum;
import com.shouqianba.cua.enums.status.DisableStatusEnum;
import com.wosai.upay.job.adapter.apollo.model.KeepAliveCheckRuleConfig;
import com.wosai.upay.job.biz.keepalive.KeepAliveProviderParamsAndDateCalculator;
import com.wosai.upay.job.biz.keepalive.validation.enums.KeepAliveCheckRuleTypeEnum;
import com.wosai.upay.job.biz.keepalive.validation.enums.KeepAliveValidationScenarioEnum;
import com.wosai.upay.job.biz.keepalive.validation.model.KeepAliveCheckRuleResult;
import com.wosai.upay.job.biz.keepalive.validation.model.KeepAliveValidateContext;
import com.wosai.upay.job.biz.keepalive.validation.rule.AbstractKeepAliveCheckRule;
import com.wosai.upay.job.refactor.dao.McAcquirerDAO;
import com.wosai.upay.job.refactor.dao.McProviderDAO;
import com.wosai.upay.job.refactor.dao.MerchantProviderParamsDAO;
import com.wosai.upay.job.refactor.model.entity.McAcquirerDO;
import com.wosai.upay.job.refactor.model.entity.McProviderDO;
import com.wosai.upay.job.refactor.model.entity.MerchantProviderParamsDO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 收单机构商户号状态规则
 *
 * <AUTHOR>
 * @date 2025/8/29
 */
@Component
public class AcquirerMerchantStatusRule extends AbstractKeepAliveCheckRule {

    @Autowired
    private McAcquirerDAO mcAcquirerDAO;
    @Autowired
    private McProviderDAO mcProviderDAO;
    @Autowired
    private KeepAliveProviderParamsAndDateCalculator calculator;
    @Autowired
    private MerchantProviderParamsDAO merchantProviderParamsDAO;

    // 添加本地缓存，避免重复查询，缓存数量50个，失效时间5分钟
    // key为provider，value为收单机构的provider
    private static final Cache<String, String> PROVIDER_ACQUIRER_PROVIDER_CACHE = CacheBuilder.newBuilder()
            .maximumSize(50)
            .expireAfterWrite(5, TimeUnit.MINUTES)
            .build();


    @Override
    protected KeepAliveCheckRuleResult doExecute(KeepAliveValidateContext context, KeepAliveCheckRuleConfig ruleConfig) {
        try {
            if (KeepAliveValidationScenarioEnum.OPEN_CIRCLE.equals(context.getKeepAliveValidationScenarioEnum())) {
                return checkForOpenCircle(context, ruleConfig);
            } else {
                return checkForPreExecuteCircleOrExecute(context, ruleConfig);
            }
        } catch (Exception e) {
            logger.error("执行收单机构商户号状态规则检查时发生异常，商户SN: {}", context.getMerchantSn(), e);
            return createFailureResult("规则执行异常: " + e.getMessage(), "EXECUTION_ERROR");
        }
    }

    private KeepAliveCheckRuleResult checkForPreExecuteCircleOrExecute(KeepAliveValidateContext context, KeepAliveCheckRuleConfig ruleConfig) {
        String provider = String.valueOf(context.getTaskDO().getProvider());
        KeepAliveCheckRuleResult result = checkAcquirerMerchantStatus(context, provider);
        if (result != null) {
            return result;
        }
        return createSuccessResult("商户收单机构商户号状态正常，通过检查");
    }

    private KeepAliveCheckRuleResult checkForOpenCircle(KeepAliveValidateContext context, KeepAliveCheckRuleConfig ruleConfig) {
        List<MerchantProviderParamsDO> merchantProviderParamsDOS = Objects.nonNull(context.getKeepAliveParams()) ? context.getKeepAliveParams() : calculator.calculateKeepAliveParams(context.getMerchantSn());
        Set<Integer> providers = merchantProviderParamsDOS.stream().map(MerchantProviderParamsDO::getProvider).collect(Collectors.toSet());

        // 收集所有失败的结果
        KeepAliveCheckRuleResult lastFailureResult = null;
        boolean hasAnySuccess = false;

        for (Integer provider : providers) {
            KeepAliveCheckRuleResult result = checkAcquirerMerchantStatus(context, String.valueOf(provider));
            if (result != null) {
                // 记录失败结果，但继续检查其他provider
                lastFailureResult = result;
            } else {
                // 至少有一个provider检查通过
                hasAnySuccess = true;
            }
        }

        // 如果有任何一个provider检查通过，则返回成功
        if (hasAnySuccess) {
            return createSuccessResult("商户收单机构商户号状态正常，通过检查");
        }

        // 所有provider都检查失败，返回最后一个失败结果
        return lastFailureResult != null ? lastFailureResult : createFailureResult("所有收单机构商户号状态检查都失败", "ACQUIRER_MERCHANT_STATUS_BLOCKED");
    }

    /**
     * 检查商户在收单机构的商户号状态
     *
     * @param context  上下文
     * @param provider 通道provider
     * @return 检查结果，如果检查通过则返回null
     */
    private KeepAliveCheckRuleResult checkAcquirerMerchantStatus(KeepAliveValidateContext context, String provider) {
        // 使用缓存优化查询
        String acquirerProvider = getAcquirerProviderFromCache(provider);
        if (acquirerProvider == null) {
            return createFailureResult(String.format("未找到 provider %s 对应的收单机构 provider信息", provider), "ACQUIRER_MERCHANT_STATUS_BLOCKED");
        }
        Optional<MerchantProviderParamsDO> acquirerParams = merchantProviderParamsDAO.getBySnAndProviderAndPayWay(context.getMerchantSn(), Integer.valueOf(acquirerProvider), PaywayEnum.ACQUIRER.getValue());
        if (!acquirerParams.isPresent()) {
            return createFailureResult(String.format("商户在收单机构没有商户号 %s，不允许执行", acquirerProvider), "ACQUIRER_MERCHANT_STATUS_BLOCKED");
        }
        if (Objects.equals(DisableStatusEnum.DISABLE.getValue(), acquirerParams.get().getDisableStatus())) {
            return createFailureResult(String.format("商户在收单机构商户号状态为禁用 %s，不允许执行", acquirerParams.get().getPayMerchantId()), "ACQUIRER_MERCHANT_STATUS_BLOCKED");
        }
        return null; // 返回null表示检查通过
    }

    /**
     * 从缓存中获取收单机构对应的provider，如果缓存中不存在则查询数据库并存入缓存
     *
     * @param provider 通道provider
     * @return 收单机构对应的provider，如果未找到则返回null
     */
    private String getAcquirerProviderFromCache(String provider) {
        try {
            return PROVIDER_ACQUIRER_PROVIDER_CACHE.get(provider, () -> {
                Optional<McProviderDO> mcProvider = mcProviderDAO.getByProvider(provider);
                if (!mcProvider.isPresent()) {
                    // 未找到provider信息，缓存null值
                    return null;
                }
                McAcquirerDO mcAcquirerDO = mcAcquirerDAO.getByAcquirer(mcProvider.get().getAcquirer());
                return mcAcquirerDO.getProvider();
            });
        } catch (Exception e) {
            logger.error("获取收单机构对应的provider时发生异常，provider: {}", provider, e);
            // 如果缓存获取失败，则直接查询
            Optional<McProviderDO> mcProvider = mcProviderDAO.getByProvider(provider);
            if (!mcProvider.isPresent()) {
                return null;
            }
            McAcquirerDO mcAcquirerDO = mcAcquirerDAO.getByAcquirer(mcProvider.get().getAcquirer());
            return mcAcquirerDO.getProvider();
        }
    }

    @Override
    public KeepAliveCheckRuleTypeEnum getRuleType() {
        return KeepAliveCheckRuleTypeEnum.ACQUIRER_MERCHANT_STATUS;
    }
}